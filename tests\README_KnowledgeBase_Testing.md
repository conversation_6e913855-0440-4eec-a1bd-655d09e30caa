# KnowledgeBase Stored Procedures Testing

This document explains how to test the KnowledgeBase stored procedures after database schema updates.

## 📋 Overview

The test suite validates all the newly created `_SYS_` stored procedures in the KnowledgeBase database to ensure they are working properly after the recent database schema changes.

## 🧪 Test Coverage

The test suite covers the following stored procedures:

### 1. **S_SYS_XRF_Entity_Attribute_Value_P_EntityID**
- **Purpose**: Retrieve entity attributes by entity ID
- **Test**: Creates test entity if needed, validates procedure execution
- **Validates**: Parameter handling, result set structure

### 2. **S_SYS_REF_Fact_P_ErrorType**
- **Purpose**: Get facts filtered by error type pattern
- **Test**: Tests with multiple error type patterns (Database, Connection, SSL, etc.)
- **Validates**: Pattern matching, result filtering, validity ratings

### 3. **S_SYS_REF_Opinion_P_ErrorType**
- **Purpose**: Get opinions filtered by error type pattern
- **Test**: Tests with multiple error type patterns
- **Validates**: Pattern matching, opinion retrieval, validity ratings

### 4. **S_SYS_REF_Fact_SelfHealer_Analytics_Prms**
- **Purpose**: Get Self-Healer analytics with filtering parameters
- **Test**: Tests various parameter combinations (category, validity, limits)
- **Validates**: Multi-parameter handling, filtering logic, result limits

### 5. **S_SYS_SelfHealer_KnowledgeSearch_Prms**
- **Purpose**: Comprehensive knowledge search across all types
- **Test**: Tests different search queries and knowledge type filters
- **Validates**: Full-text search, type filtering, multiple result sets

## 🚀 Running the Tests

### Method 1: PowerShell Script (Recommended)
```powershell
# Run from project root directory
.\Scripts\test_knowledgebase_procedures.ps1

# Run with verbose output
.\Scripts\test_knowledgebase_procedures.ps1 -Verbose

# Specify custom virtual environment path
.\Scripts\test_knowledgebase_procedures.ps1 -VenvPath ".\my_venv"
```

### Method 2: Direct Python Execution
```bash
# Ensure virtual environment is activated
.\venv\Scripts\python.exe Tests\test_knowledgebase_procedures.py
```

## 📊 Test Output

The test suite provides detailed output including:

- **Connection Status**: Database connectivity verification
- **Procedure Existence**: Confirms all procedures are installed
- **Functional Tests**: Tests each procedure with various parameters
- **Result Validation**: Verifies result structure and data quality
- **Summary Report**: Overall pass/fail statistics

### Sample Output
```
🧪 KNOWLEDGEBASE STORED PROCEDURES VALIDATION
============================================================
Connection: knowledgebase
Started: 2025-01-01 10:30:00
============================================================

🔌 Testing Database Connection...
✅ Connected to: KnowledgeBase
   SQL Server Version: Microsoft SQL Server 2017...

📋 Testing Stored Procedure Existence...
✅ Found: S_SYS_XRF_Entity_Attribute_Value_P_EntityID
   Created: 2025-01-01 10:15:23.123
   Modified: 2025-01-01 10:15:23.123
...

📊 Summary: Found 5 of 5 procedures

🔗 Testing Entity Attribute Procedure...
ℹ️  Using existing entity ID: 1
✅ Procedure executed successfully (no attributes found for entity 1)
...
```

## ✅ Success Criteria

The test suite considers the validation successful when:

1. **Database Connection**: Successfully connects to KnowledgeBase
2. **All Procedures Exist**: All 5 expected procedures are found in database
3. **Procedures Execute**: All procedures execute without errors
4. **Parameters Work**: Procedures handle various parameter combinations
5. **Results Valid**: Returned data has expected structure and content

## ❌ Troubleshooting

### Common Issues

**Connection Failures**
- Verify database server is running
- Check connection string in MCP configuration
- Ensure database name is correct

**Missing Procedures**
- Run the SQL script: `Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql`
- Check for SQL execution errors
- Verify procedure names match exactly

**Parameter Errors**
- Check stored procedure parameter definitions
- Verify data types match expectations
- Review SQL Server error logs

**No Test Data**
- Tests work with empty tables (will show "no results found")
- Consider adding sample data for more comprehensive testing
- Check if test entity creation succeeded

## 🔧 Configuration

The test script uses the following configuration:

- **Connection Name**: `knowledgebase` (from MCP database configuration)
- **Test Entity**: Creates temporary entity if none exist
- **Error Types**: Tests with common patterns (Database, Connection, SSL, etc.)
- **Search Terms**: Tests with various search queries

## 📝 Test Results

Test results are logged with:
- **Timestamp**: When each test was executed
- **Status**: PASS/FAIL for each test
- **Details**: Specific information about test execution
- **Errors**: Detailed error messages for failed tests

## 🔄 Next Steps

After successful testing:

1. **Update Documentation**: Mark procedures as validated
2. **Integration Testing**: Test with Self-Healer components
3. **Performance Testing**: Monitor procedure execution times
4. **Monitoring**: Set up alerts for procedure failures

## 📞 Support

If tests fail or you encounter issues:

1. Check the detailed error output
2. Review database logs
3. Verify SQL script execution
4. Check MCP database configuration
5. Consult the StoredProcedure_Migration_Guide.md
