/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-30
PURPOSE:   Update stored procedures to use NumericValue field for calculations
NOTES:     Modifies procedures to use NumericValue instead of parsing EntityValue text
           Resolves arithmetic overflow errors and improves performance
================================================

PREREQUISITES:
- Run schema_enhancement_numeric_values.sql first to add NumericValue and ValueUnits fields
- Ensure data migration is complete before running these procedure updates

PROCEDURES UPDATED:
- S_SYS_SelfHealer_SessionAnalytics_P: Use NumericValue for duration calculations
- S_SYS_SelfHealer_RecentSessions_P: Enhanced to show numeric data properly

================================================
*/

-- =====================================================
-- 1. UPDATE SESSION ANALYTICS PROCEDURE
-- =====================================================

PRINT 'Updating S_SYS_SelfHealer_SessionAnalytics_P to use NumericValue...'

-- Drop existing procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionAnalytics_P')
    DROP PROCEDURE S_SYS_SelfHealer_SessionAnalytics_P
GO

CREATE PROCEDURE S_SYS_SelfHealer_SessionAnalytics_P
    @DaysBack INT = 30
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME = DATEADD(day, -@DaysBack, GETDATE())
    
    SELECT 
        COUNT(*) as TotalSessions,
        SUM(CASE WHEN success_val.EntityValue LIKE '%90-100%' THEN 1 ELSE 0 END) as SuccessfulSessions,
        SUM(CASE WHEN success_val.EntityValue LIKE '%0-29%' THEN 1 ELSE 0 END) as FailedSessions,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                CAST(SUM(CASE WHEN success_val.EntityValue LIKE '%90-100%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as DECIMAL(5,2))
            ELSE 0 
        END as SuccessRate,
        
        -- ✅ FIXED: Use NumericValue for duration calculations (no more text parsing!)
        AVG(
            CASE 
                WHEN duration_val.NumericValue IS NOT NULL THEN
                    duration_val.NumericValue
                ELSE NULL 
            END
        ) as AverageHealingTime,
        
        COUNT(CASE WHEN e.CreateDate >= DATEADD(day, -1, GETDATE()) THEN 1 END) as SessionsLast24Hours,
        COUNT(CASE WHEN e.CreateDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as SessionsLastWeek
    FROM REF_Entity e
    LEFT JOIN XRF_Entity_AttributeValue eav_success ON e.ID = eav_success.EntityID
    LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID 
        AND attr_success.Name = 'Solution_Success_Rate'
    LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
    
    LEFT JOIN XRF_Entity_AttributeValue eav_duration ON e.ID = eav_duration.EntityID
    LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID 
        AND attr_duration.Name = 'Session_Duration'
    LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID
    
    WHERE e.Name LIKE 'Session_%'
    AND e.CreateDate >= @StartDate
END
GO

PRINT '✅ Updated S_SYS_SelfHealer_SessionAnalytics_P'

-- =====================================================
-- 2. UPDATE RECENT SESSIONS PROCEDURE  
-- =====================================================

PRINT 'Updating S_SYS_SelfHealer_RecentSessions_P to use NumericValue...'

-- Drop existing procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
    DROP PROCEDURE S_SYS_SelfHealer_RecentSessions_P
GO

CREATE PROCEDURE S_SYS_SelfHealer_RecentSessions_P
    @Limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@Limit)
        e.ID as EntityID,
        REPLACE(e.Name, 'Session_', '') as SessionID,
        e.CreateDate,
        
        -- ✅ ENHANCED: Show both numeric and text duration values
        COALESCE(
            CASE 
                WHEN duration_val.NumericValue IS NOT NULL AND duration_val.ValueUnits IS NOT NULL THEN
                    CAST(duration_val.NumericValue AS VARCHAR(20)) + ' ' + duration_val.ValueUnits
                ELSE duration_val.EntityValue
            END, 
            'Unknown'
        ) as Duration,
        
        -- ✅ ENHANCED: Show numeric duration for calculations
        duration_val.NumericValue as DurationNumeric,
        duration_val.ValueUnits as DurationUnits,
        
        COALESCE(success_val.EntityValue, 'Unknown') as SuccessRate,
        CASE 
            WHEN success_val.EntityValue LIKE '%90-100%' THEN 'Resolved'
            WHEN success_val.EntityValue LIKE '%0-29%' THEN 'Failed'
            ELSE 'In Progress'
        END as Status,
        CASE 
            WHEN success_val.EntityValue LIKE '%90-100%' THEN 1
            ELSE 0
        END as Success
    FROM REF_Entity e
    LEFT JOIN XRF_Entity_AttributeValue eav_duration ON e.ID = eav_duration.EntityID
    LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
        AND attr_duration.Name = 'Session_Duration'
    LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID

    LEFT JOIN XRF_Entity_AttributeValue eav_success ON e.ID = eav_success.EntityID
    LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID 
        AND attr_success.Name = 'Solution_Success_Rate'
    LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
    
    WHERE e.Name LIKE 'Session_%'
    ORDER BY e.CreateDate DESC
END
GO

PRINT '✅ Updated S_SYS_SelfHealer_RecentSessions_P'

-- =====================================================
-- 3. VERIFICATION QUERIES
-- =====================================================

PRINT 'Testing updated procedures...'

-- Test analytics procedure
PRINT 'Testing S_SYS_SelfHealer_SessionAnalytics_P:'
EXEC S_SYS_SelfHealer_SessionAnalytics_P @DaysBack = 30

-- Test recent sessions procedure  
PRINT 'Testing S_SYS_SelfHealer_RecentSessions_P:'
EXEC S_SYS_SelfHealer_RecentSessions_P @Limit = 5

PRINT '✅ Stored procedure updates complete!'
PRINT ''
PRINT 'Benefits:'
PRINT '- No more arithmetic overflow errors'
PRINT '- Faster performance (no text parsing)'
PRINT '- More accurate calculations using NumericValue'
PRINT '- Flexible display formatting using ValueUnits'
