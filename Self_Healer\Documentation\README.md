# 🔧 Self-Healer System

**Automatic error detection and resolution for N8N workflows**

## 🎯 What is Self-Healer?

Self-Healer automatically monitors N8N Builder for errors and resolves them using AI:

- **🔍 Monitors** error logs continuously
- **🧠 Analyzes** errors using local AI models  
- **🔧 Applies** fixes automatically when possible
- **📊 Tracks** all activities in a web dashboard
- **🗄️ Stores** knowledge for future use

## 🚀 Quick Start

### **Prerequisites**
- N8N Builder running
- Local AI model (LM Studio recommended)
- SQL Server database

### **Installation**
```bash
# 1. Install dependencies
pip install -r Self_Healer/requirements.txt

# 2. Configure database (edit config files)
# 3. Start Self-Healer
python Self_Healer/main.py

# 4. Access dashboard: http://localhost:8081
```

## 🏗️ How It Works

```
Error Detected → AI Analysis → Knowledge Search → Solution Applied → Results Tracked
```

## 📊 Dashboard

Access at `http://localhost:8081` for:
- Real-time error monitoring
- Healing session history  
- Success metrics
- Knowledge insights

## 📚 Documentation

- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Technical details
- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Setup guide
- **[KnowledgeBaseReadMe.md](KnowledgeBaseReadMe.md)** - Database system
- **[DB_Admin/KnowledgeBaseInfo.md](DB_Admin/KnowledgeBaseInfo.md)** - Database reference

## 🛠️ Troubleshooting

**Self-Healer not working?**
- Check dashboard at http://localhost:8081
- Verify AI model is running (LM Studio)
- Test database connection
- Check log file permissions

**Need help?**
- Run: `python Self_Healer/debug_self_healer.py`
- Test database: `python Tests/test_knowledgebase_procedures.py`

---

**🎯 Self-Healer makes your workflows more reliable by automatically fixing errors!**
