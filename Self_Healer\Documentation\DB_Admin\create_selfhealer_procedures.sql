USE KnowledgeBase
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Self-Healer specific stored procedures for KnowledgeBase integration
NOTES:     Administrative procedures protected from auto-generation utility.
           Optimized for Self-Healer knowledge retrieval and analytics.
           Follows established SQL naming conventions.
================================================
*/

-- =====================================================
-- 1. GET SESSION ATTRIBUTES BY ENTITY ID
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_XRF_Entity_Attribute_Value_P_EntityID')
    DROP PROCEDURE S_SYS_XRF_Entity_Attribute_Value_P_EntityID
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get session attributes for a specific entity ID
NOTES:     Replaces direct SQL query in knowledge_integration.py
           Returns attribute names, values, and creation dates
================================================
PARAMETERS:
   @EntityID INT - Entity ID to retrieve attributes for

RETURNS:   Attribute details with names, values, and timestamps
================================================
USAGE:

   EXEC S_SYS_XRF_Entity_Attribute_Value_P_EntityID @EntityID = 123

================================================
SUPPORT QUERIES:

   -- Check if entity exists
   SELECT ID, Name FROM REF_Entity WHERE ID = 123

   -- List all attributes for debugging
   SELECT * FROM REF_Attribute ORDER BY Name

================================================
*/

CREATE PROCEDURE S_SYS_XRF_Entity_Attribute_Value_P_EntityID
    @EntityID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        a.Name as AttributeName, 
        ev.EntityValue, 
        eav.CreateDate
    FROM XRF_Entity_AttributeValue eav
    JOIN REF_Attribute a ON eav.AttributeID = a.ID
    JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
    WHERE eav.EntityID = @EntityID
    ORDER BY a.Name, eav.CreateDate DESC
END
GO

-- =====================================================
-- 2. GET FACTS BY ERROR TYPE WITH SELF-HEALER FILTER
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Fact_P_ErrorType')
    DROP PROCEDURE S_SYS_REF_Fact_P_ErrorType
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get facts related to specific error type for Self-Healer
NOTES:     Filters for Self-Healer data source and orders by validity rating
           Supports pattern matching for flexible error type queries
================================================
PARAMETERS:
   @ErrorType NVARCHAR(255) - Error type to search for (supports wildcards)

RETURNS:   Facts with ID, Name, ValidityRating, DataSource, CreateDate
================================================
USAGE:

   EXEC S_SYS_REF_Fact_P_ErrorType @ErrorType = 'JSON_Parsing_Error'
   EXEC S_SYS_REF_Fact_P_ErrorType @ErrorType = '%Database%'

================================================
SUPPORT QUERIES:

   -- List all Self-Healer facts
   SELECT Name, ValidityRating FROM REF_Fact 
   WHERE DataSource LIKE '%Self-Healer%' ORDER BY ValidityRating DESC

================================================
*/

CREATE PROCEDURE S_SYS_REF_Fact_P_ErrorType
    @ErrorType NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        f.ID, 
        f.Name, 
        f.ValidityRating, 
        f.DataSource, 
        f.CreateDate
    FROM REF_Fact f
    WHERE (f.Name LIKE '%' + @ErrorType + '%' OR f.DataSource LIKE '%' + @ErrorType + '%')
    AND f.DataSource LIKE '%Self-Healer%'
    ORDER BY f.ValidityRating DESC, f.CreateDate DESC
END
GO

-- =====================================================
-- 3. GET OPINIONS BY ERROR TYPE WITH VALIDITY FILTER
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Opinion_P_ErrorType')
    DROP PROCEDURE S_SYS_REF_Opinion_P_ErrorType
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get opinions related to specific error type for Self-Healer
NOTES:     Filters for Self-Healer data source and orders by validity rating
           Searches both opinion name and content for comprehensive results
================================================
PARAMETERS:
   @ErrorType NVARCHAR(255) - Error type to search for (supports wildcards)

RETURNS:   Opinions with ID, Name, ValidityRating, Opinion, DataSource, CreateDate
================================================
USAGE:

   EXEC S_SYS_REF_Opinion_P_ErrorType @ErrorType = 'Database_Connection'
   EXEC S_SYS_REF_Opinion_P_ErrorType @ErrorType = '%SSL%'

================================================
*/

CREATE PROCEDURE S_SYS_REF_Opinion_P_ErrorType
    @ErrorType NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        o.ID, 
        o.Name, 
        o.ValidityRating, 
        o.Opinion, 
        o.DataSource, 
        o.CreateDate
    FROM REF_Opinion o
    WHERE (o.Name LIKE '%' + @ErrorType + '%' OR o.Opinion LIKE '%' + @ErrorType + '%')
    AND o.DataSource LIKE '%Self-Healer%'
    ORDER BY o.ValidityRating DESC, o.CreateDate DESC
END
GO

-- =====================================================
-- 4. GET SOLUTION EFFECTIVENESS ANALYTICS
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Fact_SelfHealer_Analytics_Prms')
    DROP PROCEDURE S_SYS_REF_Fact_SelfHealer_Analytics_Prms
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get solution effectiveness analytics for Self-Healer dashboard
NOTES:     Aggregates validity ratings, evidence counts, and trending data
           Supports filtering by category and minimum validity threshold
================================================
PARAMETERS:
   @Category NVARCHAR(255) - Optional category filter (NULL for all)
   @MinValidity DECIMAL(5,2) - Minimum validity rating filter (0 for all)
   @Limit INT - Maximum number of results to return

RETURNS:   Analytics data with effectiveness metrics and trending information
================================================
USAGE:

   -- Get all analytics
   EXEC S_SYS_REF_Fact_SelfHealer_Analytics_Prms
        @Category = NULL, @MinValidity = 0, @Limit = 50

   -- Get high-validity JSON error solutions
   EXEC S_SYS_REF_Fact_SelfHealer_Analytics_Prms
        @Category = 'JSON', @MinValidity = 70.0, @Limit = 10

================================================
*/

CREATE PROCEDURE S_SYS_REF_Fact_SelfHealer_Analytics_Prms
    @Category NVARCHAR(255) = NULL,
    @MinValidity DECIMAL(5,2) = 0,
    @Limit INT = 50
AS
BEGIN
    SET NOCOUNT ON;

    SELECT TOP (@Limit)
        f.ID,
        f.Name,
        f.ValidityRating,
        f.DataSource,
        f.CreateDate,
        COUNT(e.ID) as EvidenceCount,
        AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage,
        CASE
            WHEN f.CreateDate > DATEADD(day, -7, GETDATE()) AND f.ValidityRating > 70.0
            THEN 1 ELSE 0
        END as IsTrending
    FROM REF_Fact f
    LEFT JOIN REF_Evidence e ON f.ID = e.FactID
    WHERE f.DataSource LIKE '%Self-Healer%'
    AND (@Category IS NULL OR f.Name LIKE '%' + @Category + '%')
    AND f.ValidityRating >= @MinValidity
    GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
    ORDER BY f.ValidityRating DESC, f.CreateDate DESC
END
GO

-- =====================================================
-- 5. COMPREHENSIVE KNOWLEDGE SEARCH
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_KnowledgeSearch_Prms')
    DROP PROCEDURE S_SYS_SelfHealer_KnowledgeSearch_Prms
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Comprehensive knowledge search across facts, opinions, and evidence
NOTES:     Returns multiple result sets for different knowledge types
           Optimized for Self-Healer full-text search functionality
================================================
PARAMETERS:
   @SearchQuery NVARCHAR(255) - Search term to find across all knowledge
   @KnowledgeType NVARCHAR(50) - Type filter: 'fact', 'opinion', 'evidence', or NULL for all
   @MinValidity DECIMAL(5,2) - Minimum validity rating filter
   @Limit INT - Maximum results per knowledge type

RETURNS:   Multiple result sets: Facts, Opinions, Evidence (based on KnowledgeType)
================================================
USAGE:

   -- Search all knowledge types
   EXEC S_SYS_SelfHealer_KnowledgeSearch_Prms
        @SearchQuery = 'database connection', @KnowledgeType = NULL,
        @MinValidity = 50.0, @Limit = 10

   -- Search only facts
   EXEC S_SYS_SelfHealer_KnowledgeSearch_Prms
        @SearchQuery = 'JSON parsing', @KnowledgeType = 'fact',
        @MinValidity = 70.0, @Limit = 5

================================================
*/

CREATE PROCEDURE S_SYS_SelfHealer_KnowledgeSearch_Prms
    @SearchQuery NVARCHAR(255),
    @KnowledgeType NVARCHAR(50) = NULL,
    @MinValidity DECIMAL(5,2) = 0,
    @Limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;

    -- Search Facts
    IF @KnowledgeType IS NULL OR @KnowledgeType = 'fact'
    BEGIN
        SELECT TOP (@Limit)
            f.ID,
            f.Name,
            f.ValidityRating,
            f.DataSource,
            f.CreateDate,
            'fact' as KnowledgeType
        FROM REF_Fact f
        WHERE (f.Name LIKE '%' + @SearchQuery + '%' OR f.DataSource LIKE '%' + @SearchQuery + '%')
        AND f.DataSource LIKE '%Self-Healer%'
        AND f.ValidityRating >= @MinValidity
        ORDER BY f.ValidityRating DESC, f.CreateDate DESC
    END

    -- Search Opinions
    IF @KnowledgeType IS NULL OR @KnowledgeType = 'opinion'
    BEGIN
        SELECT TOP (@Limit)
            o.ID,
            o.Name,
            o.ValidityRating,
            o.Opinion,
            o.DataSource,
            o.CreateDate,
            'opinion' as KnowledgeType
        FROM REF_Opinion o
        WHERE (o.Name LIKE '%' + @SearchQuery + '%'
               OR o.Opinion LIKE '%' + @SearchQuery + '%'
               OR o.DataSource LIKE '%' + @SearchQuery + '%')
        AND o.DataSource LIKE '%Self-Healer%'
        AND o.ValidityRating >= @MinValidity
        ORDER BY o.ValidityRating DESC, o.CreateDate DESC
    END

    -- Search Evidence
    IF @KnowledgeType IS NULL OR @KnowledgeType = 'evidence'
    BEGIN
        SELECT TOP (@Limit)
            e.ID,
            e.Name,
            e.Evidence,
            e.DataSource,
            e.CreateDate,
            f.ValidityRating,
            'evidence' as KnowledgeType
        FROM REF_Evidence e
        JOIN REF_Fact f ON e.FactID = f.ID
        WHERE (e.Name LIKE '%' + @SearchQuery + '%'
               OR e.Evidence LIKE '%' + @SearchQuery + '%'
               OR e.DataSource LIKE '%' + @SearchQuery + '%')
        AND e.DataSource LIKE '%Self-Healer%'
        ORDER BY f.ValidityRating DESC, e.CreateDate DESC
    END
END
GO

PRINT 'Self-Healer stored procedures created successfully!'
PRINT 'Created procedures:'
PRINT '  - S_SYS_XRF_Entity_Attribute_Value_P_EntityID'
PRINT '  - S_SYS_REF_Fact_P_ErrorType'
PRINT '  - S_SYS_REF_Opinion_P_ErrorType'
PRINT '  - S_SYS_REF_Fact_SelfHealer_Analytics_Prms'
PRINT '  - S_SYS_SelfHealer_KnowledgeSearch_Prms'
