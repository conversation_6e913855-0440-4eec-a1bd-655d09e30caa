USE KnowledgeBase
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-30
PURPOSE:   Fix XRF table names to match documented SQL naming conventions
NOTES:     Renames XRF tables from CamelCase to proper underscore format.
           Updates all references in existing procedures and constraints.
           Must be run BEFORE regenerating CRUD procedures.
================================================

BACKGROUND:
- Current tables use CamelCase: XRF_EntityAttributeValue, XRF_CategoryFact
- Convention requires underscores: XRF_Entity_Attribute_Value, XRF_Category_Fact
- This ensures automation tools work properly with consistent naming

EXECUTION ORDER:
1. Run this script to rename tables and update references
2. Regenerate all CRUD procedures using automation utility
3. Update any custom _SYS_ procedures to use new table names

================================================
*/

PRINT 'Starting XRF table naming convention fixes...'
PRINT ''

-- =====================================================
-- 1. RENAME XRF TABLES TO PROPER CONVENTION
-- =====================================================

PRINT '1. Renaming XRF tables to match naming convention:'

-- XRF_CategoryEvidence -> XRF_Category_Evidence
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryEvidence')
BEGIN
    EXEC sp_rename 'XRF_CategoryEvidence', 'XRF_Category_Evidence'
    PRINT '  ✅ XRF_CategoryEvidence -> XRF_Category_Evidence'
END
ELSE
    PRINT '  ⚠️ XRF_CategoryEvidence not found'

-- XRF_CategoryFact -> XRF_Category_Fact
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryFact')
BEGIN
    EXEC sp_rename 'XRF_CategoryFact', 'XRF_Category_Fact'
    PRINT '  ✅ XRF_CategoryFact -> XRF_Category_Fact'
END
ELSE
    PRINT '  ⚠️ XRF_CategoryFact not found'

-- XRF_CategoryOpinion -> XRF_Category_Opinion
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryOpinion')
BEGIN
    EXEC sp_rename 'XRF_CategoryOpinion', 'XRF_Category_Opinion'
    PRINT '  ✅ XRF_CategoryOpinion -> XRF_Category_Opinion'
END
ELSE
    PRINT '  ⚠️ XRF_CategoryOpinion not found'

-- XRF_CrossCorrelation -> XRF_Cross_Correlation
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CrossCorrelation')
BEGIN
    EXEC sp_rename 'XRF_CrossCorrelation', 'XRF_Cross_Correlation'
    PRINT '  ✅ XRF_CrossCorrelation -> XRF_Cross_Correlation'
END
ELSE
    PRINT '  ⚠️ XRF_CrossCorrelation not found'

-- XRF_EntityAttributeValue -> XRF_Entity_Attribute_Value
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_EntityAttributeValue')
BEGIN
    EXEC sp_rename 'XRF_EntityAttributeValue', 'XRF_Entity_Attribute_Value'
    PRINT '  ✅ XRF_EntityAttributeValue -> XRF_Entity_Attribute_Value'
END
ELSE
    PRINT '  ⚠️ XRF_EntityAttributeValue not found'

-- XRF_EntityCategoryEntityAttributeValue -> XRF_Entity_Category_Entity_Attribute_Value
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_EntityCategoryEntityAttributeValue')
BEGIN
    EXEC sp_rename 'XRF_EntityCategoryEntityAttributeValue', 'XRF_Entity_Category_Entity_Attribute_Value'
    PRINT '  ✅ XRF_EntityCategoryEntityAttributeValue -> XRF_Entity_Category_Entity_Attribute_Value'
END
ELSE
    PRINT '  ⚠️ XRF_EntityCategoryEntityAttributeValue not found'

PRINT ''

-- =====================================================
-- 2. UPDATE EXISTING STORED PROCEDURES
-- =====================================================

PRINT '2. Updating existing stored procedures to use new table names:'

-- Update S_SYS_SelfHealer_RecentSessions_P if it references old table names
PRINT '  📝 Checking S_SYS_SelfHealer_RecentSessions_P...'
-- Note: This procedure may need manual review for table name updates

-- Update S_SYS_SelfHealer_SessionAnalytics_P if it references old table names  
PRINT '  📝 Checking S_SYS_SelfHealer_SessionAnalytics_P...'
-- Note: This procedure may need manual review for table name updates

PRINT ''

-- =====================================================
-- 3. VERIFICATION
-- =====================================================

PRINT '3. Verifying renamed tables:'

SELECT 
    'RENAMED_TABLES' as QueryType,
    TABLE_NAME,
    CASE 
        WHEN TABLE_NAME LIKE 'XRF_%_%' THEN '✅ Correct Format'
        WHEN TABLE_NAME LIKE 'XRF_%' AND TABLE_NAME NOT LIKE 'XRF_%_%' THEN '❌ Needs Underscore'
        ELSE '❓ Unknown Format'
    END as NamingStatus
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
AND TABLE_NAME LIKE 'XRF_%'
ORDER BY TABLE_NAME

PRINT ''
PRINT '✅ XRF table naming convention fixes complete!'
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Regenerate all CRUD procedures using automation utility'
PRINT '2. Update Self-Healer _SYS_ procedures to reference new table names'
PRINT '3. Test all procedures to ensure they work with renamed tables'
PRINT '4. Update any application code that references old table names'
