# KnowledgeBase Dynamic Schema Information

**Location**: `Self_Healer/Documentation/DB_Admin/`
**Created**: 2025-06-27
**Updated**: 2025-01-01 - Added validated stored procedures
**Purpose**: Dynamic schema retrieval system for Self-Healer KnowledgeBase integration

## 📋 Overview

This folder contains database administration tools for dynamically retrieving KnowledgeBase schema information. Instead of static schema documentation, this system provides real-time, up-to-date database structure information.

## 🔧 Database Administration Tools

### **1. Stored Procedure: `sp_GetKnowledgeBaseSchema`**

**File**: `create_schema_procedure.sql`  
**Purpose**: SQL Server stored procedure for dynamic schema retrieval

**Features**:
- **Dynamic table information** - Get all tables or specific table details
- **Complete column metadata** - Data types, constraints, relationships
- **Real-time row counts** - Current data volume information
- **Relationship mapping** - Foreign key relationships between tables
- **Flexible parameters** - Optional table name parameter

**Usage**:
```sql
-- Get all tables
EXEC sp_GetKnowledgeBaseSchema

-- Get specific table
EXEC sp_GetKnowledgeBaseSchema @TableName = 'REF_Entities'
```

### **2. Python Schema Retriever: `get_knowledgebase_schema.py`**

**File**: `get_knowledgebase_schema.py`
**Purpose**: Python script that uses the stored procedure to retrieve and format schema information

**Features**:
- **Multiple output formats** - Text, JSON, Markdown
- **Command-line interface** - Easy to use from scripts or manually
- **Structured data processing** - Organized schema information
- **Error handling** - Robust database connection management

**Usage**:
```bash
# Get all tables (text format)
python get_knowledgebase_schema.py

# Get specific table
python get_knowledgebase_schema.py REF_Entities

# Get all tables as JSON
python get_knowledgebase_schema.py --format json

# Get specific table as Markdown
python get_knowledgebase_schema.py REF_Fact --format markdown
```

### **3. Self-Healer Stored Procedures: `create_selfhealer_procedures.sql`** ✅ **VALIDATED**

**File**: `create_selfhealer_procedures.sql`
**Purpose**: Optimized stored procedures for Self-Healer KnowledgeBase operations
**Status**: ✅ **Successfully tested and validated** (2025-01-01)

**Implemented Procedures**:
- **`S_SYS_XRF_Entity_Attribute_Value_P_EntityID`** - Retrieve entity attributes by ID
- **`S_SYS_REF_Fact_P_ErrorType`** - Get facts filtered by error type pattern
- **`S_SYS_REF_Opinion_P_ErrorType`** - Get opinions filtered by error type pattern
- **`S_SYS_REF_Fact_SelfHealer_Analytics_Prms`** - Self-Healer analytics with parameters
- **`S_SYS_SelfHealer_KnowledgeSearch_Prms`** - Comprehensive knowledge search

**Benefits**:
- **Performance**: Pre-compiled and cached execution plans
- **Security**: SQL injection prevention through parameterization
- **Maintainability**: Centralized business logic in database layer
- **Testing**: Individual procedures validated with comprehensive test suite

### **4. Comprehensive Test Suite: `test_knowledgebase_procedures.py`** ✅ **COMPLETED**

**File**: `Tests/test_knowledgebase_procedures.py`
**Purpose**: Validates all stored procedures functionality
**Status**: ✅ **All tests passed successfully** (2025-01-01)

**Test Coverage**:
- **Database connectivity** verification
- **Procedure existence** confirmation
- **Parameter handling** with various input combinations
- **Result set validation** and data quality checks
- **Error handling** and edge case testing

**Execution**:
```bash
# Run comprehensive test suite
.\Scripts\test_knowledgebase_procedures.ps1

# Run with verbose output
.\Scripts\test_knowledgebase_procedures.ps1 -Verbose
```

## 📊 Current KnowledgeBase Structure ✅ **VALIDATED**

**Database**: KnowledgeBase
**Connection**: Uses MCP Database Tool with 'knowledgebase' connection
**Schema Status**: ✅ **Updated to SQL naming conventions and validated** (2025-01-01)

### **Available Tables** (Following SQL Naming Conventions):
- **REF_Entity** - Entity definitions (renamed from REF_Entities)
- **REF_Fact** - Factual information with validity ratings
- **REF_Opinion** - Opinion data linked to facts
- **REF_Category** - Category classifications
- **REF_Attribute** - Attribute definitions (renamed from REF_Attributes)
- **XRF_Entity_Category** - Entity-category cross-reference (renamed)
- **REF_EntityValue** - Entity attribute values (renamed from REF_EntityValues)
- **REF_Evidence** - Supporting evidence for facts/opinions

### **Stored Procedures** (Optimized for Self-Healer):
- **`S_SYS_XRF_Entity_Attribute_Value_P_EntityID`** ✅ Validated
- **`S_SYS_REF_Fact_P_ErrorType`** ✅ Validated
- **`S_SYS_REF_Opinion_P_ErrorType`** ✅ Validated
- **`S_SYS_REF_Fact_SelfHealer_Analytics_Prms`** ✅ Validated
- **`S_SYS_SelfHealer_KnowledgeSearch_Prms`** ✅ Validated

### **Key Column Mappings for Self-Healer Integration**:

| Self-Healer Expected | Actual KnowledgeBase Column |
|---------------------|----------------------------|
| `EntityID`          | `ID`                       |
| `EntityName`        | `Name`                     |
| `FactID`            | `ID`                       |
| `FactText`          | `Name`                     |
| `DateCreated`       | `CreateDate`               |

## 🎯 Integration Benefits

### **Dynamic Schema Awareness**
- **Always current** - Schema information is retrieved in real-time
- **No maintenance** - No need to update static documentation
- **Flexible queries** - Can get overview or detailed information as needed

### **Self-Healer Integration**
- **Column mapping** - Clear understanding of actual vs expected column names
- **Data validation** - Real-time row counts and data presence verification
- **Relationship understanding** - Foreign key relationships for proper data insertion

### **Development Efficiency**
- **Quick reference** - Fast access to current database structure
- **Multiple formats** - Choose the best format for your current need
- **Scriptable** - Can be integrated into automated processes

## 🚀 Usage Examples

### **For Development Reference**:
```bash
# Quick overview of all tables
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py

# Detailed view of entities table
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py REF_Entities
```

### **For Documentation Generation**:
```bash
# Generate markdown documentation
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py --format markdown > current_schema.md
```

### **For Automated Scripts**:
```bash
# Get JSON for programmatic processing
python Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py --format json > schema.json
```

## 📁 File Organization

```
Self_Healer/Documentation/DB_Admin/
├── KnowledgeBaseInfo.md                    # This file - overview and usage
├── create_schema_procedure.sql             # SQL to create schema retrieval procedure
├── create_selfhealer_procedures.sql        # ✅ Self-Healer optimized procedures (VALIDATED)
├── get_knowledgebase_schema.py             # Python script to retrieve schema
└── StoredProcedure_Migration_Guide.md     # Migration guide and documentation

Tests/
├── test_knowledgebase_procedures.py        # ✅ Comprehensive test suite (ALL TESTS PASSED)
└── README_KnowledgeBase_Testing.md         # Testing documentation and usage guide

Scripts/
└── test_knowledgebase_procedures.ps1       # PowerShell test runner script
```

## 🔄 Maintenance

### **To Update Schema Retrieval Procedure**:
1. Edit `create_schema_procedure.sql`
2. Execute the SQL script in SQL Server Management Studio or via MCP Database Tool
3. The procedure will be dropped and recreated with new functionality

### **To Update Self-Healer Procedures** ✅ **VALIDATED**:
1. Edit `create_selfhealer_procedures.sql`
2. Execute the SQL script to update procedures
3. **Run validation tests**: `.\Scripts\test_knowledgebase_procedures.ps1`
4. Verify all tests pass before deploying changes

### **To Enhance the Python Schema Tool**:
1. Edit `get_knowledgebase_schema.py`
2. Add new output formats, processing logic, or command-line options
3. Test with various table names and parameters

### **To Update Test Suite**:
1. Edit `Tests/test_knowledgebase_procedures.py`
2. Add new test cases for additional procedures or scenarios
3. Update test documentation in `Tests/README_KnowledgeBase_Testing.md`
4. Run tests to ensure all functionality works correctly

## 🎯 Future Enhancements

### **Planned Features**:
- **Schema comparison** - Compare current schema with previous versions
- **Data profiling** - Statistical analysis of table contents
- **Index information** - Performance optimization insights
- **Dependency mapping** - Complete relationship visualization
- **Performance monitoring** - Track stored procedure execution statistics
- **Automated testing** - Continuous integration for database changes

### **Integration Opportunities**:
- **Self-Healer auto-configuration** - Automatically adapt to schema changes
- **Monitoring dashboards** - Real-time schema and data health monitoring
- **Automated documentation** - Generate comprehensive database documentation
- **CI/CD Integration** - Automated testing in deployment pipelines

### **Completed Enhancements** ✅:
- **Stored Procedure Migration** - Optimized database operations (2025-01-01)
- **Comprehensive Testing** - Full validation test suite (2025-01-01)
- **SQL Naming Conventions** - Updated table names to follow standards (2025-01-01)
- **Performance Optimization** - Pre-compiled stored procedures for better performance

## 📞 Support

For questions about the KnowledgeBase schema or these tools:
1. **Check current schema**: Run `get_knowledgebase_schema.py` for latest information
2. **Review stored procedures**: Check `create_selfhealer_procedures.sql` for Self-Healer procedures
3. **Run validation tests**: Execute `.\Scripts\test_knowledgebase_procedures.ps1` to verify functionality
4. **Test connections**: Verify MCP Database Tool connectivity to 'knowledgebase'
5. **Review naming conventions**: See [SQLConventions.md](../SQLConventions.md) for database standards
6. **Check test documentation**: See `Tests/README_KnowledgeBase_Testing.md` for testing guidance

## 🎉 Current Status Summary

✅ **Database Schema**: Updated to SQL naming conventions
✅ **Stored Procedures**: 5 Self-Healer procedures implemented and validated
✅ **Test Suite**: Comprehensive testing completed successfully
✅ **Documentation**: Updated with current implementation status
✅ **Integration Ready**: Self-Healer can now use optimized stored procedures

**Last Validated**: 2025-01-01
**Next Review**: Scheduled for next major Self-Healer update

**Remember**: This system provides dynamic, real-time schema information and validated stored procedures - always use these tools for the most current database structure details!
