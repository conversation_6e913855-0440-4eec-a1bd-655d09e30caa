/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-30
PURPOSE:   Enhance REF_EntityValues table with numeric value support
NOTES:     Adds NumericValue (FLOAT) and ValueUnits (VARCHAR(50)) fields
           to support both text and numeric data storage without breaking
           existing functionality. Resolves arithmetic overflow errors
           in stored procedures that need to perform calculations.
================================================

BACKGROUND:
- REF_EntityValues currently stores all values as text (EntityValue NVARCHAR)
- Self-Healer stores durations as "23.5 seconds", success rates as "90-100%", etc.
- Stored procedures trying to convert text to numeric cause overflow errors
- This enhancement allows storing numeric values alongside text for calculations

SOLUTION:
- Add NumericValue (FLOAT) for mathematical operations
- Add ValueUnits (VARCHAR(50)) for unit information and display formatting
- Maintain EntityValue for backward compatibility and human-readable display
- Update procedures to use NumericValue for calculations, ValueUnits for formatting

================================================
*/

-- =====================================================
-- 1. ALTER TABLE - ADD NUMERIC SUPPORT FIELDS
-- =====================================================

PRINT 'Adding NumericValue and ValueUnits fields to REF_EntityValues...'

-- Check if columns already exist before adding them
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'REF_EntityValues' AND COLUMN_NAME = 'NumericValue')
BEGIN
    ALTER TABLE REF_EntityValues 
    ADD NumericValue FLOAT NULL
    PRINT '✅ Added NumericValue (FLOAT) column'
END
ELSE
BEGIN
    PRINT '⚠️ NumericValue column already exists'
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'REF_EntityValues' AND COLUMN_NAME = 'ValueUnits')
BEGIN
    ALTER TABLE REF_EntityValues 
    ADD ValueUnits VARCHAR(50) NULL
    PRINT '✅ Added ValueUnits (VARCHAR(50)) column'
END
ELSE
BEGIN
    PRINT '⚠️ ValueUnits column already exists'
END

-- =====================================================
-- 2. DATA MIGRATION - PARSE EXISTING VALUES
-- =====================================================

PRINT 'Migrating existing data to new numeric fields...'

-- Update Duration Values (e.g., "23.5 seconds" -> NumericValue: 23.5, ValueUnits: "seconds")
UPDATE REF_EntityValues 
SET 
    NumericValue = CASE 
        WHEN EntityValue LIKE '%._ seconds' AND ISNUMERIC(REPLACE(EntityValue, ' seconds', '')) = 1 THEN
            CAST(REPLACE(EntityValue, ' seconds', '') AS FLOAT)
        ELSE NULL
    END,
    ValueUnits = CASE 
        WHEN EntityValue LIKE '%seconds' THEN 'seconds'
        ELSE NULL
    END
WHERE EntityValue LIKE '%seconds'
AND NumericValue IS NULL  -- Only update if not already set

PRINT '✅ Updated duration values (seconds)'

-- Update Success Rate Values (e.g., "90-100%" -> NumericValue: 95.0, ValueUnits: "percentage")
UPDATE REF_EntityValues 
SET 
    NumericValue = CASE 
        WHEN EntityValue = '90-100%' THEN 95.0
        WHEN EntityValue = '70-89%' THEN 79.5
        WHEN EntityValue = '50-69%' THEN 59.5
        WHEN EntityValue = '30-49%' THEN 39.5
        WHEN EntityValue = '0-29%' THEN 14.5
        ELSE NULL
    END,
    ValueUnits = CASE 
        WHEN EntityValue LIKE '%-%' AND EntityValue LIKE '%\%%' ESCAPE '\' THEN 'percentage'
        ELSE NULL
    END
WHERE EntityValue LIKE '%-%' AND EntityValue LIKE '%\%%' ESCAPE '\'
AND NumericValue IS NULL  -- Only update if not already set

PRINT '✅ Updated success rate percentage values'

-- Update Simple Percentage Values (e.g., "75%" -> NumericValue: 75.0, ValueUnits: "percentage")
UPDATE REF_EntityValues 
SET 
    NumericValue = CASE 
        WHEN EntityValue LIKE '%\%%' ESCAPE '\' AND ISNUMERIC(REPLACE(EntityValue, '%', '')) = 1 THEN
            CAST(REPLACE(EntityValue, '%', '') AS FLOAT)
        ELSE NULL
    END,
    ValueUnits = CASE 
        WHEN EntityValue LIKE '%\%%' ESCAPE '\' THEN 'percentage'
        ELSE NULL
    END
WHERE EntityValue LIKE '%\%%' ESCAPE '\'
AND EntityValue NOT LIKE '%-%'  -- Exclude range percentages (already handled above)
AND NumericValue IS NULL  -- Only update if not already set

PRINT '✅ Updated simple percentage values'

-- Update Millisecond Values (e.g., "1500ms" -> NumericValue: 1500.0, ValueUnits: "milliseconds")
UPDATE REF_EntityValues 
SET 
    NumericValue = CASE 
        WHEN EntityValue LIKE '%ms' AND ISNUMERIC(REPLACE(EntityValue, 'ms', '')) = 1 THEN
            CAST(REPLACE(EntityValue, 'ms', '') AS FLOAT)
        ELSE NULL
    END,
    ValueUnits = CASE 
        WHEN EntityValue LIKE '%ms' THEN 'milliseconds'
        ELSE NULL
    END
WHERE EntityValue LIKE '%ms'
AND NumericValue IS NULL  -- Only update if not already set

PRINT '✅ Updated millisecond values'

-- =====================================================
-- 3. VERIFICATION QUERIES
-- =====================================================

PRINT 'Verification of data migration:'

-- Show sample of migrated data
SELECT TOP 10
    Name,
    EntityValue,
    NumericValue,
    ValueUnits,
    CreateDate
FROM REF_EntityValues 
WHERE NumericValue IS NOT NULL
ORDER BY CreateDate DESC

PRINT '✅ Schema enhancement complete!'
PRINT ''
PRINT 'Next Steps:'
PRINT '1. Update stored procedures to use NumericValue for calculations'
PRINT '2. Update Self-Healer code to populate new fields'
PRINT '3. Test functionality with new schema'
