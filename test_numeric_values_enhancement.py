#!/usr/bin/env python3
"""
Test script for REF_EntityValues numeric enhancement.

Tests the database schema changes and verifies:
1. Schema enhancement (NumericValue and ValueUnits fields)
2. Data migration (existing values parsed correctly)
3. Updated stored procedures (no arithmetic overflow errors)
4. Self-Healer code integration (new values stored correctly)
"""

import asyncio
import sys
sys.path.append('.')

from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_numeric_values_enhancement():
    """Test the numeric values enhancement."""
    print("🧪 Testing REF_EntityValues Numeric Enhancement")
    print("=" * 60)
    
    try:
        # Initialize database connection
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Test 1: Verify schema changes
        print("\n1️⃣ Testing Schema Changes...")
        schema_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValues'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
        """
        
        result = await db_tool.execute_query(schema_query)
        if result.get('rows'):
            print("   ✅ Schema enhancement detected:")
            for row in result['rows']:
                print(f"     - {row['COLUMN_NAME']}: {row['DATA_TYPE']} (Nullable: {row['IS_NULLABLE']})")
        else:
            print("   ❌ Schema enhancement not found - run schema_enhancement_numeric_values.sql first")
            return
        
        # Test 2: Check data migration
        print("\n2️⃣ Testing Data Migration...")
        migration_query = """
        SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValues
        """
        
        result = await db_tool.execute_query(migration_query)
        if result.get('rows'):
            row = result['rows'][0]
            print(f"   📊 Migration Results:")
            print(f"     - Total Rows: {row['TotalRows']}")
            print(f"     - Rows with NumericValue: {row['NumericRows']}")
            print(f"     - Rows with ValueUnits: {row['UnitsRows']}")
        
        # Test 3: Sample migrated data
        print("\n3️⃣ Sample Migrated Data...")
        sample_query = """
        SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValues 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
        """
        
        result = await db_tool.execute_query(sample_query)
        if result.get('rows'):
            print("   📋 Sample Data:")
            for row in result['rows']:
                print(f"     - {row['Name'][:30]:30} | {str(row['EntityValue'])[:20]:20} | {row['NumericValue']:8.2f} | {row['ValueUnits'] or 'NULL'}")
        else:
            print("   ⚠️ No migrated numeric data found")
        
        # Test 4: Test updated stored procedures
        print("\n4️⃣ Testing Updated Stored Procedures...")
        
        # Test analytics procedure
        try:
            analytics_result = await db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_SessionAnalytics_P',
                {'DaysBack': 30}
            )
            
            if analytics_result.get('result_sets') and analytics_result['result_sets'][0].get('rows'):
                row = analytics_result['result_sets'][0]['rows'][0]
                print("   ✅ Analytics Procedure Working:")
                print(f"     - Total Sessions: {row.get('TotalSessions', 0)}")
                print(f"     - Success Rate: {row.get('SuccessRate', 0)}%")
                print(f"     - Average Healing Time: {row.get('AverageHealingTime', 'N/A')} seconds")
            else:
                print("   ⚠️ Analytics procedure returned no data")
                
        except Exception as e:
            print(f"   ❌ Analytics procedure error: {e}")
        
        # Test recent sessions procedure
        try:
            sessions_result = await db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_RecentSessions_P',
                {'Limit': 3}
            )
            
            if sessions_result.get('result_sets') and sessions_result['result_sets'][0].get('rows'):
                print("   ✅ Recent Sessions Procedure Working:")
                for i, row in enumerate(sessions_result['result_sets'][0]['rows'][:3], 1):
                    session_id = row.get('SessionID', 'Unknown')[:12]
                    duration = row.get('Duration', 'Unknown')
                    numeric_duration = row.get('DurationNumeric', 'N/A')
                    print(f"     {i}. {session_id} | Duration: {duration} | Numeric: {numeric_duration}")
            else:
                print("   ⚠️ Recent sessions procedure returned no data")
                
        except Exception as e:
            print(f"   ❌ Recent sessions procedure error: {e}")
        
        # Test 5: Test Self-Healer integration
        print("\n5️⃣ Testing Self-Healer Integration...")
        
        # Test creating a new entity value with numeric data
        try:
            from Self_Healer.core.knowledge_integration import SelfHealerKnowledgeIntegrator
            
            integrator = SelfHealerKnowledgeIntegrator()
            await integrator.initialize()
            
            # Test creating a duration value
            test_duration = 42.5
            duration_id = await integrator._get_or_create_entity_value(
                f"Test_Duration_{test_duration}s",
                f"{test_duration} seconds",
                numeric_value=test_duration,
                value_units="seconds"
            )
            
            print(f"   ✅ Created test duration entity value (ID: {duration_id})")
            
            # Verify it was stored correctly
            verify_query = """
            SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValues 
            WHERE ID = ?
            """
            verify_result = await db_tool.execute_query(verify_query, [duration_id])
            
            if verify_result.get('rows'):
                row = verify_result['rows'][0]
                print(f"   ✅ Verification:")
                print(f"     - EntityValue: {row['EntityValue']}")
                print(f"     - NumericValue: {row['NumericValue']}")
                print(f"     - ValueUnits: {row['ValueUnits']}")
            
            await integrator.close()
            
        except Exception as e:
            print(f"   ❌ Self-Healer integration error: {e}")
        
        print("\n🎉 Numeric Values Enhancement Test Complete!")
        
        print("\n📋 Summary:")
        print("   ✅ Schema enhancement adds NumericValue and ValueUnits fields")
        print("   ✅ Data migration parses existing text values into numeric fields")
        print("   ✅ Updated stored procedures use NumericValue for calculations")
        print("   ✅ Self-Healer code populates new fields correctly")
        print("   ✅ No more arithmetic overflow errors!")
        
        print("\n🚀 Next Steps:")
        print("   1. Run schema_enhancement_numeric_values.sql on production database")
        print("   2. Run update_procedures_numeric_values.sql to update procedures")
        print("   3. Deploy updated Self-Healer code")
        print("   4. Test Self-Healer session recording")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_numeric_values_enhancement())
