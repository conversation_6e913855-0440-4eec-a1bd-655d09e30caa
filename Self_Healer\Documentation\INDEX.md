# Self-Healer Documentation

## 📚 Main Documentation

- **[README.md](README.md)** - System overview and quick start
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Technical architecture  
- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Setup and integration
- **[KnowledgeBaseReadMe.md](KnowledgeBaseReadMe.md)** - Database system

## 🗄️ Database Administration

- **[SQLConventions.md](SQLConventions.md)** - Database naming conventions
- **[DB_Admin/KnowledgeBaseInfo.md](DB_Admin/KnowledgeBaseInfo.md)** - Database reference

## 🎯 Quick Navigation

**For Users:** Start with [README.md](README.md)  
**For Developers:** See [ARCHITECTURE.md](ARCHITECTURE.md)  
**For Database:** Check [DB_Admin/](DB_Admin/) folder

---

**🎯 Self-Healer is fully operational with KnowledgeBase integration and real-time monitoring!**
