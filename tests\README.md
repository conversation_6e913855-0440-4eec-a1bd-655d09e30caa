# 🧪 N8N Builder Test Suite

**Comprehensive testing framework for N8N Builder and Self-Healer integration**

## 📋 Overview

This test suite provides comprehensive validation of the N8N Builder system, including database connectivity, network processes, Self-Healer integration, system health monitoring, and MCP Research Tool functionality.

## 🎯 Test Categories

### **1. System Health Tests** (`test_system_health.py`)
- **Database Connectivity**: Tests MCP Database Tool and stored procedures
- **Network Process Validation**: Checks port availability and N8N processes
- **Self-Healer Integration**: Validates Self-Healer components and functionality
- **System Resources**: Monitors CPU, memory, and disk usage
- **Configuration Validation**: Checks configuration files and settings
- **File System Health**: Validates project structure and log management

### **2. Stored Procedures Tests** (`test_stored_procedures.py`)
- **Database Connection**: Verifies KnowledgeBase connectivity
- **Procedure Existence**: Confirms all stored procedures are installed
- **Functionality Testing**: Tests each stored procedure with sample data
- **Performance Validation**: Checks procedure execution times
- **Error Handling**: Validates error scenarios and edge cases

### **3. MCP Research Tool Tests**
- **`test_mcp_research.py`** - Core MCP research functionality tests
- **`test_complete_integration.py`** - Full system integration tests
- **`test_research_quality.py`** - Research quality and performance tests

### **4. Core System Tests**
- **`test_n8n_builder_unit.py`** - Main N8N Builder functionality
- **`test_project_management.py`** - Project management and workflow validation
- **`test_mcp_database.py`** - Database integration tests

## 🚀 Quick Start

### **Run All Tests**
```bash
# Run comprehensive system test suite
python tests/run_system_tests.py

# Or run individual test suites
python tests/test_system_health.py
python tests/test_stored_procedures.py
```

### **Using Pytest**
```bash
# Run all tests with pytest
pytest tests/ -v

# Run specific test files
pytest tests/test_system_health.py -v
pytest tests/test_stored_procedures.py -v

# Run with coverage
pytest tests/ --cov=n8n_builder --cov=Self_Healer
```

### **Individual Test Files**
```bash
# System health check
python tests/test_system_health.py

# Database and stored procedures
python tests/test_stored_procedures.py

# MCP Research Tool tests
python tests/test_mcp_research.py

# Complete integration tests
python tests/test_complete_integration.py
```

## 📊 Test Results

### **Status Indicators**
- **✅ HEALTHY/PASS**: All tests passed, system is functioning normally
- **⚠️ WARNING**: Some issues detected, system functional but needs attention
- **❌ CRITICAL/FAIL**: Critical failures detected, immediate attention required
- **❓ UNKNOWN**: Test status could not be determined

### **Result Files**
Test results are automatically saved to:
- `tests/integration_results/system_health_results.json` - Detailed health check results
- Console output with real-time status updates
- Performance metrics and timing information

### **Expected Test Results**
```
🧪 N8N Builder System Test Suite
================================================================================
🏥 System Health Check: ✅ HEALTHY
🗄️ Stored Procedures Test: ✅ COMPLETED
⚡ Quick Integration Tests: ✅ PASS

📊 FINAL TEST SUMMARY
================================================================================
✅ Overall Status: HEALTHY
🕒 Total Duration: 45.2 seconds
📋 Test Suite Results:
   ✅ System Health: HEALTHY (12.3s)
   ✅ Stored Procedures: COMPLETED (8.7s)
   ✅ Quick Integration: PASS (2.1s)
```

## 🔧 Configuration

### **Test Configuration** (`test_config.json`)
```json
{
  "agent": {
    "name": "test_n8n_workflow_builder",
    "max_concurrent_workflows": 3,
    "timeout": 60
  },
  "security": {
    "enable_authentication": false,
    "rate_limiting": { "enabled": false }
  },
  "monitoring": {
    "enabled": true,
    "log_level": "DEBUG"
  }
}
```

### **Database Configuration**
Tests automatically use the main configuration from `n8n_builder.config.Config()`:
- **KnowledgeBase Connection**: Tests MCP Database Tool connectivity
- **Stored Procedures**: Validates Self-Healer specific procedures
- **Performance Metrics**: Measures query execution times

### **Environment Setup**
Tests automatically configure the necessary environment:
- **Import paths** - Configured to work from tests directory
- **Mock data** - Included for offline testing
- **Cache handling** - Temporary cache for test isolation
- **Error simulation** - Network failure and timeout testing

### **Dependencies**
All test dependencies are included in the main `requirements.txt`:
- `pytest>=8.0.0` - Test framework
- `pytest-asyncio>=0.23.0` - Async test support
- `psutil>=5.9.7` - System monitoring
- `beautifulsoup4>=4.12.2` - HTML parsing (MCP Research)
- `lxml>=4.9.3` - XML/HTML processing

## 🐛 Troubleshooting

### **Common Issues**

#### **Import Errors**
```
ModuleNotFoundError: No module named 'n8n_builder'
```
**Solution**: Tests automatically add parent directory to path. If issues persist, run from project root:
```bash
cd /path/to/N8N_Builder
python tests/test_mcp_research.py
```

#### **Network Timeouts**
```
HTTP Request timeout
```
**Solution**: Tests include retry logic and graceful degradation. Network failures are expected and handled.

#### **Cache Conflicts**
```
Enhanced cache put failed
```
**Solution**: Tests automatically fall back to simple cache. This is expected behavior.

## 📈 Performance Benchmarks

### **Typical Test Performance**
- **test_mcp_research.py**: ~30-60 seconds (includes network calls)
- **test_complete_integration.py**: ~45-90 seconds (full system test)
- **test_research_quality.py**: ~60-120 seconds (comprehensive quality tests)

### **Network-Dependent Tests**
Some tests make real HTTP requests to:
- n8n official documentation
- n8n community forum
- GitHub repositories (rate limited)

These tests may occasionally fail due to:
- Network connectivity issues
- Rate limiting (HTTP 429)
- Service availability (HTTP 404, 503)

This is expected and does not indicate system failure.

## 🔍 Test Details

### **test_mcp_research.py**
- **Purpose**: Core MCP research tool functionality
- **Coverage**: Offline processing, online research, caching
- **Duration**: ~30-60 seconds
- **Network**: Yes (with fallback)

### **test_complete_integration.py**
- **Purpose**: Full system integration testing
- **Coverage**: End-to-end workflow generation with research
- **Duration**: ~45-90 seconds
- **Network**: Yes (with fallback)

### **test_research_quality.py**
- **Purpose**: Research quality and performance optimization
- **Coverage**: Multiple workflow types, accuracy metrics
- **Duration**: ~60-120 seconds
- **Network**: Yes (extensive)

## 📝 Adding New Tests

### **Test File Template**
```python
#!/usr/bin/env python3
"""
Test Description

Brief description of what this test covers.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports when running from tests folder
sys.path.insert(0, str(Path(__file__).parent.parent))

# Your test imports and code here
```

### **Best Practices**
1. **Include import path setup** for standalone execution
2. **Use descriptive test names** and docstrings
3. **Handle network failures gracefully** with try/except
4. **Include both positive and negative test cases**
5. **Document expected behavior** in comments

## 🎯 Test Goals

The test suite ensures:
- ✅ **Reliability** - System works consistently
- ✅ **Performance** - Acceptable response times
- ✅ **Error Handling** - Graceful failure modes
- ✅ **Integration** - Components work together
- ✅ **Quality** - Research results meet standards

## 📚 Related Documentation

- **[MCP Research Setup Guide](../Documentation/MCP_RESEARCH_SETUP_GUIDE.md)** - Integration details
- **[API Documentation](../Documentation/API_DOCUMENTATION.md)** - API testing reference
- **[Technical Architecture](../Documentation/DOCUMENTATION.MD)** - System design

---

**🧪 Ready to test?** Run `python tests/test_mcp_research.py` to get started!
