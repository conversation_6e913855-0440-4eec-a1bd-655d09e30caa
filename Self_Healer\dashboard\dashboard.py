"""
Self-Healer Dashboard - Web-based monitoring interface.

This module provides a web-based dashboard for monitoring the Self-Healer
system's activities, metrics, and health status.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from websockets.exceptions import ConnectionClosedError
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# Import existing N8N Builder components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from n8n_builder.logging_config import get_logger
from Self_Healer.core.healer_manager import SelfHealerManager
from Self_Healer.api.knowledge_endpoints import knowledge_router


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects."""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class SelfHealerDashboard:
    """
    Web-based dashboard for monitoring the Self-Healer system.
    
    Features:
    - Real-time system status monitoring
    - Healing session tracking
    - Performance metrics visualization
    - Learning system insights
    - Manual control interface
    """
    
    def __init__(self, healer_manager: SelfHealerManager, port: int = 8081):
        """Initialize the dashboard."""
        self.logger = get_logger('self_healer.dashboard')
        self.healer_manager = healer_manager
        self.port = port
        
        # FastAPI app
        self.app = FastAPI(title="Self-Healer Dashboard", version="1.0.0")

        # Include KnowledgeBase API routes
        self.app.include_router(knowledge_router)

        # WebSocket connections for real-time updates
        self.websocket_connections: List[WebSocket] = []

        # Dashboard data cache
        self.dashboard_data: Dict[str, Any] = {}
        self.last_update = datetime.now()

        # Setup routes
        self._setup_routes()
        
        # Background tasks
        self.update_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        self.logger.info(f"Self-Healer Dashboard initialized on port {port}")
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """Main dashboard page."""
            return self._get_dashboard_html()
        
        @self.app.get("/api/status")
        async def get_status():
            """Get current system status."""
            try:
                status = await self.healer_manager.get_status()
                # Convert to JSON string using DateTimeEncoder, then parse back for JSONResponse
                json_str = json.dumps(status, cls=DateTimeEncoder)
                return JSONResponse(content=json.loads(json_str))
            except Exception as e:
                self.logger.error(f"Error getting status: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/metrics")
        async def get_metrics():
            """Get system metrics."""
            try:
                metrics = await self._get_comprehensive_metrics()
                # Convert to JSON string using DateTimeEncoder, then parse back for JSONResponse
                json_str = json.dumps(metrics, cls=DateTimeEncoder)
                return JSONResponse(content=json.loads(json_str))
            except Exception as e:
                self.logger.error(f"Error getting metrics: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/sessions")
        async def get_sessions():
            """Get healing sessions."""
            try:
                sessions = await self._get_session_data()
                return JSONResponse(content=sessions)
            except Exception as e:
                self.logger.error(f"Error getting sessions: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)

        @self.app.get("/api/session/{session_id}")
        async def get_session_details(session_id: str):
            """Get detailed information about a specific session."""
            try:
                details = await self._get_session_details(session_id)
                return JSONResponse(content=details)
            except Exception as e:
                self.logger.error(f"Error getting session details for {session_id}: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)

        @self.app.get("/api/error-type/{error_type}")
        async def get_error_type_info(error_type: str):
            """Get information about a specific error type."""
            try:
                info = await self._get_error_type_info(error_type)
                return JSONResponse(content=info)
            except Exception as e:
                self.logger.error(f"Error getting error type info for {error_type}: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/learning")
        async def get_learning_data():
            """Get learning system data."""
            try:
                learning_data = self.healer_manager.learning_engine.get_learning_statistics()
                return JSONResponse(content=learning_data)
            except Exception as e:
                self.logger.error(f"Error getting learning data: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.post("/api/emergency_stop")
        async def emergency_stop():
            """Emergency stop the healing system."""
            try:
                await self.healer_manager.emergency_stop()
                return JSONResponse(content={"status": "emergency_stop_activated"})
            except Exception as e:
                self.logger.error(f"Error during emergency stop: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)

        @self.app.post("/api/rescan_logs")
        async def rescan_logs():
            """Force a rescan of log files for recent errors."""
            try:
                new_errors_found = await self.healer_manager.error_monitor.force_rescan_logs(hours_back=2)
                return JSONResponse(content={
                    "status": "rescan_completed",
                    "message": f"Log rescan completed successfully - found {new_errors_found} new errors",
                    "new_errors_found": new_errors_found
                })
            except Exception as e:
                self.logger.error(f"Error during log rescan: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)

        @self.app.post("/api/rescan_historical")
        async def rescan_historical():
            """Force a rescan of all historical log entries."""
            try:
                # Scan the last 30 days to capture all historical errors
                new_errors_found = await self.healer_manager.error_monitor.force_rescan_logs(hours_back=720)  # 30 days
                return JSONResponse(content={
                    "status": "historical_rescan_completed",
                    "message": f"Historical rescan completed - found {new_errors_found} new errors",
                    "new_errors_found": new_errors_found
                })
            except Exception as e:
                self.logger.error(f"Error during historical rescan: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)

        @self.app.get("/api/healing_stats")
        async def get_healing_stats():
            """Get healing statistics from log analysis."""
            try:
                stats = self.healer_manager.error_monitor.get_healing_statistics_from_log(hours_back=24)
                return JSONResponse(content=stats)
            except Exception as e:
                self.logger.error(f"Error getting healing stats: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await self._handle_websocket(websocket)
    
    def _get_dashboard_html(self) -> str:
        """Generate dashboard HTML."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Self-Healer Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #3498db; }
        .metric-label { color: #7f8c8d; margin-top: 5px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-running { background-color: #27ae60; }
        .status-stopped { background-color: #e74c3c; }
        .status-warning { background-color: #f39c12; }
        .sessions-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; }
        .sessions-table th, .sessions-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        .sessions-table th { background-color: #34495e; color: white; }
        .emergency-button { background-color: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .emergency-button:hover { background-color: #c0392b; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section h2 { margin-top: 0; color: #2c3e50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Self-Healer Dashboard</h1>
            <p>Real-time monitoring of the N8N Builder Self-Healing System</p>
            <div id="system-status">
                <span class="status-indicator status-running"></span>
                <span>System Status: <span id="status-text">Loading...</span></span>
            </div>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="total-errors">-</div>
                <div class="metric-label">Total Errors Detected</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="success-rate">-</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="active-sessions">-</div>
                <div class="metric-label">Active Sessions</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="avg-healing-time">-</div>
                <div class="metric-label">Avg Healing Time (s)</div>
            </div>
        </div>
        
        <div class="section">
            <h2>Recent Healing Sessions</h2>
            <table class="sessions-table" id="sessions-table">
                <thead>
                    <tr>
                        <th>Session ID</th>
                        <th>Error Type</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Success</th>
                    </tr>
                </thead>
                <tbody id="sessions-tbody">
                    <tr><td colspan="5">Loading...</td></tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>Learning System</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="total-patterns">-</div>
                    <div class="metric-label">Total Patterns</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="learning-records">-</div>
                    <div class="metric-label">Learning Records</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="effectiveness-score">-</div>
                    <div class="metric-label">Avg Effectiveness</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>System Controls</h2>
            <button class="emergency-button" onclick="emergencyStop()">Emergency Stop</button>
            <p><small>Use emergency stop only in critical situations. This will halt all healing activities.</small></p>
        </div>
    </div>
    
    <script>
        let ws = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 10;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);

            ws.onopen = function() {
                console.log('WebSocket connected');
                reconnectAttempts = 0; // Reset on successful connection
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };

            ws.onclose = function(event) {
                console.log('WebSocket closed:', event.code, event.reason);

                // Don't reconnect if it's a normal close or service restart
                if (event.code === 1000 || event.code === 1001) {
                    return; // Normal closure, don't reconnect
                }

                // Exponential backoff for reconnection
                if (reconnectAttempts < maxReconnectAttempts) {
                    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                    console.log(`Reconnecting in ${delay}ms (attempt ${reconnectAttempts + 1})`);
                    setTimeout(connectWebSocket, delay);
                    reconnectAttempts++;
                } else {
                    console.error('Max reconnection attempts reached');
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }
        
        function updateDashboard(data) {
            if (data.status) {
                document.getElementById('status-text').textContent = data.status.status;
                document.getElementById('total-errors').textContent = data.status.metrics.total_errors_detected;
                document.getElementById('success-rate').textContent = data.status.metrics.success_rate.toFixed(1) + '%';
                document.getElementById('active-sessions').textContent = data.status.active_sessions;
                document.getElementById('avg-healing-time').textContent = data.status.metrics.average_healing_time.toFixed(1);
                
                // Update status indicator
                const indicator = document.querySelector('.status-indicator');
                indicator.className = 'status-indicator ' + (data.status.is_running ? 'status-running' : 'status-stopped');
            }
            
            if (data.sessions) {
                updateSessionsTable(data.sessions);
            }
            
            if (data.learning) {
                document.getElementById('total-patterns').textContent = data.learning.total_patterns;
                document.getElementById('learning-records').textContent = data.learning.total_learning_records;
                document.getElementById('effectiveness-score').textContent = data.learning.average_effectiveness_score.toFixed(2);
            }
        }
        
        function updateSessionsTable(sessions) {
            const tbody = document.getElementById('sessions-tbody');
            tbody.innerHTML = '';

            if (sessions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5">No recent sessions</td></tr>';
                return;
            }

            sessions.forEach(session => {
                const row = tbody.insertRow();

                // Make session ID clickable
                const sessionIdLink = `<a href="#" onclick="showSessionDetails('${session.session_id}')" style="color: #007bff; text-decoration: underline;">${session.session_id.substring(0, 12)}...</a>`;

                // Make error type more meaningful
                const errorType = getMeaningfulErrorType(session.error_id);
                const errorTypeLink = `<a href="#" onclick="showErrorTypeInfo('${session.error_id}')" style="color: #28a745; text-decoration: underline;">${errorType}</a>`;

                // Make status clickable if resolved
                let statusDisplay = session.status;
                if (session.status === 'Resolved') {
                    statusDisplay = `<a href="#" onclick="showResolutionDetails('${session.session_id}')" style="color: #28a745; text-decoration: underline;">${session.status}</a>`;
                }

                row.innerHTML = `
                    <td>${sessionIdLink}</td>
                    <td>${errorTypeLink}</td>
                    <td>${statusDisplay}</td>
                    <td>${session.duration.toFixed(1)}s</td>
                    <td>${session.success ? '✅' : '❌'}</td>
                `;
            });
        }

        function getMeaningfulErrorType(errorId) {
            // Convert technical error IDs to user-friendly names
            const errorTypeMapping = {
                'error_log_entry': 'General Error',
                'llm_communication': 'LLM Connection',
                'json_parsing': 'JSON Processing',
                'workflow_structure': 'Workflow Structure',
                'validation': 'Validation Error',
                'network': 'Network Error',
                'database': 'Database Error',
                'configuration': 'Configuration Error'
            };

            // Extract error type from error ID
            for (const [key, value] of Object.entries(errorTypeMapping)) {
                if (errorId.toLowerCase().includes(key)) {
                    return value;
                }
            }

            // Default: clean up the error ID
            return errorId.replace('Error_', '').replace('_', ' ');
        }

        async function showSessionDetails(sessionId) {
            try {
                const response = await fetch(`/api/session/${sessionId}`);
                const details = await response.json();

                if (details.error) {
                    alert(`Error loading session details: ${details.error}`);
                    return;
                }

                let detailsHtml = `
                    <h3>Session Details: ${sessionId}</h3>
                    <p><strong>Found:</strong> ${details.found ? 'Yes' : 'No'}</p>
                `;

                if (details.found) {
                    detailsHtml += `
                        <h4>Error Information:</h4>
                        <p><strong>Type:</strong> ${details.error_details.type || 'Unknown'}</p>
                        <p><strong>Message:</strong> ${details.error_details.message || 'No message available'}</p>

                        <h4>Solution Information:</h4>
                        <p><strong>Solution Applied:</strong> ${details.solution_details.solution || 'No solution recorded'}</p>
                        <p><strong>Status:</strong> ${details.solution_details.status || 'Unknown'}</p>
                        <p><strong>Success Rate:</strong> ${details.solution_details.success_rate || 'Unknown'}</p>

                        <h4>All Attributes:</h4>
                        <ul>
                    `;

                    details.attributes.forEach(attr => {
                        detailsHtml += `<li><strong>${attr.name}:</strong> ${attr.value}</li>`;
                    });

                    detailsHtml += '</ul>';
                }

                detailsHtml += `<p><strong>Resolution Summary:</strong> ${details.resolution_summary}</p>`;

                showModal('Session Details', detailsHtml);

            } catch (error) {
                alert(`Error loading session details: ${error.message}`);
            }
        }

        async function showErrorTypeInfo(errorId) {
            try {
                const response = await fetch(`/api/error-type/${errorId}`);
                const info = await response.json();

                if (info.error) {
                    alert(`Error loading error type info: ${info.error}`);
                    return;
                }

                let infoHtml = `
                    <h3>Error Type: ${info.display_name}</h3>
                    <p><strong>Description:</strong> ${info.description}</p>
                    <p><strong>Solutions Available:</strong> ${info.solutions_count}</p>
                    <p><strong>Average Effectiveness:</strong> ${info.average_effectiveness.toFixed(1)}%</p>
                `;

                if (info.recommendations && info.recommendations.length > 0) {
                    infoHtml += '<h4>Recommended Solutions:</h4><ul>';
                    info.recommendations.forEach(rec => {
                        infoHtml += `<li><strong>${rec.solution}</strong> (${rec.effectiveness}% effective) - Source: ${rec.source}</li>`;
                    });
                    infoHtml += '</ul>';
                } else {
                    infoHtml += '<p>No specific recommendations available for this error type.</p>';
                }

                showModal('Error Type Information', infoHtml);

            } catch (error) {
                alert(`Error loading error type info: ${error.message}`);
            }
        }

        async function showResolutionDetails(sessionId) {
            try {
                const response = await fetch(`/api/session/${sessionId}`);
                const details = await response.json();

                if (details.error) {
                    alert(`Error loading resolution details: ${details.error}`);
                    return;
                }

                let resolutionHtml = `
                    <h3>Resolution Details for Session: ${sessionId}</h3>
                    <p><strong>Resolution Summary:</strong> ${details.resolution_summary}</p>
                `;

                if (details.found && details.solution_details) {
                    resolutionHtml += `
                        <h4>Solution Applied:</h4>
                        <p>${details.solution_details.solution || 'No specific solution recorded'}</p>

                        <h4>Resolution Status:</h4>
                        <p>${details.solution_details.status || 'Status unknown'}</p>

                        <h4>Success Rate:</h4>
                        <p>${details.solution_details.success_rate || 'Not measured'}</p>
                    `;
                } else {
                    resolutionHtml += '<p>No detailed resolution information available.</p>';
                }

                showModal('Resolution Details', resolutionHtml);

            } catch (error) {
                alert(`Error loading resolution details: ${error.message}`);
            }
        }

        function showModal(title, content) {
            // Create modal if it doesn't exist
            let modal = document.getElementById('details-modal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'details-modal';
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                `;

                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    max-width: 80%;
                    max-height: 80%;
                    overflow-y: auto;
                    position: relative;
                `;

                const closeButton = document.createElement('button');
                closeButton.innerHTML = '×';
                closeButton.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                `;
                closeButton.onclick = () => modal.style.display = 'none';

                modalContent.appendChild(closeButton);
                modalContent.innerHTML += `<div id="modal-content"></div>`;
                modal.appendChild(modalContent);
                document.body.appendChild(modal);

                // Close modal when clicking outside
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                };
            }

            // Update content and show
            document.getElementById('modal-content').innerHTML = content;
            modal.style.display = 'flex';
        }

        async function emergencyStop() {
            if (confirm('Are you sure you want to perform an emergency stop? This will halt all healing activities.')) {
                try {
                    const response = await fetch('/api/emergency_stop', { method: 'POST' });
                    const result = await response.json();
                    alert('Emergency stop activated');
                } catch (error) {
                    alert('Error during emergency stop: ' + error.message);
                }
            }
        }
        
        // Initialize dashboard
        connectWebSocket();
        
        // Periodic updates as fallback
        setInterval(async () => {
            try {
                const [statusResponse, learningResponse] = await Promise.all([
                    fetch('/api/status'),
                    fetch('/api/learning')
                ]);
                
                const status = await statusResponse.json();
                const learning = await learningResponse.json();
                
                updateDashboard({ status, learning });
            } catch (error) {
                console.error('Error updating dashboard:', error);
            }
        }, 10000); // Update every 10 seconds
    </script>
</body>
</html>
        """
    
    async def _handle_websocket(self, websocket: WebSocket):
        """Handle WebSocket connection."""
        await websocket.accept()
        self.websocket_connections.append(websocket)

        try:
            while True:
                # Send periodic updates (reduced from 5 to 30 seconds to prevent log spam)
                await asyncio.sleep(30)

                # Get current data
                data = await self._get_dashboard_data()
                await websocket.send_text(json.dumps(data, cls=DateTimeEncoder))

        except WebSocketDisconnect as e:
            # Normal disconnection - don't log as error
            if websocket in self.websocket_connections:
                self.websocket_connections.remove(websocket)
            # Only log if it's not a normal "going away" or "service restart" disconnection
            if e.code not in [1001, 1012]:  # 1001 = going away, 1012 = service restart
                self.logger.info(f"WebSocket disconnected: {e.code}")
        except ConnectionClosedError as e:
            # Handle connection closed errors gracefully
            if websocket in self.websocket_connections:
                self.websocket_connections.remove(websocket)
            self.logger.debug(f"WebSocket connection closed: {e}")
        except Exception as e:
            # Only log unexpected errors
            error_str = str(e).lower()
            if "1012" not in error_str and "service restart" not in error_str:
                self.logger.error(f"WebSocket error: {e}")
            if websocket in self.websocket_connections:
                self.websocket_connections.remove(websocket)
    
    async def _get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data from KnowledgeBase."""
        try:
            # Get status from healer manager (for system status)
            status = await self.healer_manager.get_status()

            # Get learning stats from healer manager
            learning_stats = self.healer_manager.learning_engine.get_learning_statistics()

            # Get sessions from KnowledgeBase
            sessions = await self._get_session_data()

            # Get analytics from KnowledgeBase
            analytics = await self._get_session_analytics()

            # Merge analytics into status
            if analytics:
                status['metrics'].update({
                    'total_errors_detected': analytics.get('TotalSessions', 0),
                    'successful_healings': analytics.get('SuccessfulSessions', 0),
                    'failed_healings': analytics.get('FailedSessions', 0),
                    'success_rate': analytics.get('SuccessRate', 0.0),
                    'average_healing_time': analytics.get('AverageHealingTime', 0.0)
                })

            return {
                'status': status,
                'learning': learning_stats,
                'sessions': sessions,
                'analytics': analytics,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting dashboard data: {e}")
            return {'error': str(e)}

    async def _get_session_analytics(self) -> Dict[str, Any]:
        """Get session analytics from KnowledgeBase."""
        try:
            # Import the knowledge database wrapper
            from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB

            # Create database connection
            knowledge_db = SelfHealerKnowledgeDB('knowledgebase')

            # Get analytics using stored procedure
            result = await knowledge_db.db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_SessionAnalytics_P',
                {'DaysBack': 30}
            )

            if result.get('result_sets') and len(result['result_sets']) > 0:
                result_set = result['result_sets'][0]
                if result_set.get('rows') and len(result_set['rows']) > 0:
                    row = result_set['rows'][0]
                    return {
                        'TotalSessions': row.get('TotalSessions', 0),
                        'SuccessfulSessions': row.get('SuccessfulSessions', 0),
                        'FailedSessions': row.get('FailedSessions', 0),
                        'SuccessRate': float(row.get('SuccessRate') or 0.0),
                        'AverageHealingTime': float(row.get('AverageHealingTime') or 0.0),
                        'SessionsLast24Hours': row.get('SessionsLast24Hours', 0),
                        'SessionsLastWeek': row.get('SessionsLastWeek', 0)
                    }

            return {}

        except Exception as e:
            self.logger.error(f"Failed to get analytics from KnowledgeBase: {e}")
            return {}
    
    async def _get_comprehensive_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        status = await self.healer_manager.get_status()
        learning_stats = self.healer_manager.learning_engine.get_learning_statistics()
        
        return {
            'system_metrics': status['metrics'],
            'learning_metrics': learning_stats,
            'timestamp': datetime.now().isoformat()
        }
    
    async def _get_session_data(self) -> List[Dict[str, Any]]:
        """Get healing session data from KnowledgeBase using stored procedure."""
        try:
            # Import the MCP database tool directly
            from n8n_builder.mcp_database_tool import MCPDatabaseTool

            # Create database connection
            db_tool = MCPDatabaseTool('knowledgebase')

            # Use stored procedure for better performance and consistency
            result = await db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_RecentSessions_P',
                {'Limit': 10}
            )

            sessions = []

            if result['status'] == 'success' and result.get('result_sets'):
                # Stored procedure returns result_sets instead of rows
                for result_set in result['result_sets']:
                    for row in result_set.get('rows', []):
                        session_id = row.get('SessionID', 'Unknown')
                        entity_id = row.get('EntityID', 0)

                        # Use data from stored procedure (already processed)
                        status = row.get('Status', 'Unknown')
                        success = row.get('Success', 0) == 1
                        duration_str = row.get('Duration', 'Unknown')
                        success_rate = row.get('SuccessRate', 'Unknown')

                        # Parse duration for numeric value
                        duration = self._parse_duration(duration_str) if duration_str != 'Unknown' else 0.0

                        # Determine error type based on status
                        error_type = 'Database Error' if status == 'Failed' else 'General Error'

                        # Create session data using stored procedure results
                        session = {
                            'session_id': session_id,
                            'error_id': error_type,  # Use meaningful error type
                            'status': status,
                            'success': success,
                            'duration': duration,
                            'start_time': row.get('CreateDate', '').replace('T', ' ') if row.get('CreateDate') else '',
                            'end_time': None  # Not stored separately in current schema
                        }
                        sessions.append(session)

            self.logger.debug(f"Retrieved {len(sessions)} sessions from KnowledgeBase")
            return sessions

        except Exception as e:
            self.logger.error(f"Failed to get sessions from KnowledgeBase: {e}")
            # Fallback to in-memory data if database fails
            try:
                status = await self.healer_manager.get_status()
                return status.get('recent_sessions', [])
            except:
                return []

    def _parse_duration(self, duration_str: str) -> float:
        """Parse duration string to float seconds."""
        try:
            if 'seconds' in duration_str:
                return float(duration_str.replace(' seconds', '').replace('s', ''))
            return 0.0
        except:
            return 0.0

    async def _get_session_details(self, session_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific healing session."""
        try:
            # Import the MCP database tool directly
            from n8n_builder.mcp_database_tool import MCPDatabaseTool

            # Create database connection
            db_tool = MCPDatabaseTool('knowledgebase')

            session_details = {
                'session_id': session_id,
                'found': False,
                'attributes': [],
                'error_details': {},
                'solution_details': {},
                'resolution_summary': 'No resolution information available',
                'facts': [],
                'evidence': []
            }

            # First, find the session entity
            session_query = """
            SELECT ID, Name, CreateDate
            FROM REF_Entities
            WHERE Name = ? OR Name LIKE ?
            """
            session_result = await db_tool.execute_query(session_query, [f'Session_{session_id}', f'%{session_id}%'])

            if not session_result.get('rows'):
                session_details['resolution_summary'] = f'Session {session_id} not found in database'
                return session_details

            session_row = session_result['rows'][0]
            entity_id = session_row['ID']
            session_details['found'] = True
            session_details['create_date'] = session_row.get('CreateDate', '')

            # Get session attributes using XRF_EntityAttributeValue with numeric support
            attributes_query = """
            SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attributes a ON eav.AttributeID = a.ID
            JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
            """
            attributes_result = await db_tool.execute_query(attributes_query, [entity_id])

            if attributes_result.get('rows'):
                for row in attributes_result['rows']:
                    attr_name = row.get('AttributeName', '')
                    attr_value = row.get('AttributeValue', '')
                    numeric_value = row.get('NumericValue')
                    value_units = row.get('ValueUnits')

                    # Enhanced attribute display with numeric support
                    attribute_info = {
                        'name': attr_name,
                        'value': attr_value,
                        'numeric_value': numeric_value,
                        'units': value_units,
                        'create_date': row.get('CreateDate', ''),
                        'display_value': self._format_attribute_display(attr_value, numeric_value, value_units)
                    }

                    session_details['attributes'].append(attribute_info)

                    # Parse specific attributes for better display
                    if 'error' in attr_name.lower():
                        session_details['error_details'][attr_name.lower().replace('_', '')] = attribute_info['display_value']
                    elif 'solution' in attr_name.lower() or 'success' in attr_name.lower():
                        session_details['solution_details'][attr_name.lower().replace('_', '')] = attribute_info['display_value']

            # Get related facts (solutions) for this session
            facts_query = """
            SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
            """
            facts_result = await db_tool.execute_query(facts_query, [f'%{session_id}%', f'%{session_id}%'])

            if facts_result.get('rows'):
                session_details['facts'] = facts_result['rows']

            # Get related evidence for this session
            evidence_query = """
            SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
            """
            evidence_result = await db_tool.execute_query(evidence_query, [f'%{session_id}%', f'%{session_id}%'])

            if evidence_result.get('rows'):
                session_details['evidence'] = evidence_result['rows']

            # Build resolution summary from available data
            if session_details['found']:
                summary_parts = []

                if session_details['solution_details']:
                    for key, value in session_details['solution_details'].items():
                        summary_parts.append(f"{key.title()}: {value}")

                if session_details['facts']:
                    best_fact = session_details['facts'][0]  # Highest validity rating
                    summary_parts.append(f"Best Solution: {best_fact['Name']} ({best_fact['ValidityRating']}% effective)")

                if session_details['evidence']:
                    summary_parts.append(f"Evidence Records: {len(session_details['evidence'])}")

                if summary_parts:
                    session_details['resolution_summary'] = " | ".join(summary_parts)
                else:
                    session_details['resolution_summary'] = f"Session found but no detailed resolution data available (Created: {session_details['create_date']})"

            return session_details

        except Exception as e:
            self.logger.error(f"Failed to get session details for {session_id}: {e}")
            return {
                'session_id': session_id,
                'found': False,
                'error': str(e),
                'resolution_summary': f'Error retrieving details: {str(e)}'
            }

    async def _get_error_type_info(self, error_type: str) -> Dict[str, Any]:
        """Get information about a specific error type."""
        try:
            # Import the MCP database tool directly
            from n8n_builder.mcp_database_tool import MCPDatabaseTool

            # Create database connection
            db_tool = MCPDatabaseTool('knowledgebase')

            # Build meaningful error type information
            error_info = {
                'error_type': error_type,
                'display_name': self._get_meaningful_error_name(error_type),
                'description': f'Information about {self._get_meaningful_error_name(error_type)} errors',
                'facts': [],
                'opinions': [],
                'solutions_count': 0,
                'average_effectiveness': 0,
                'recommendations': [],
                'related_sessions': []
            }

            # Get facts related to this error type
            facts_query = """
            SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
            """
            facts_result = await db_tool.execute_query(facts_query, [f'%{error_type}%', f'%{error_type}%'])

            if facts_result.get('rows'):
                error_info['facts'] = facts_result['rows']
                error_info['solutions_count'] = len(facts_result['rows'])

                # Calculate average effectiveness
                if facts_result['rows']:
                    total_validity = sum(float(fact.get('ValidityRating') or 0) for fact in facts_result['rows'])
                    error_info['average_effectiveness'] = total_validity / len(facts_result['rows'])

            # Get opinions related to this error type
            opinions_query = """
            SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
            """
            opinions_result = await db_tool.execute_query(opinions_query, [f'%{error_type}%', f'%{error_type}%'])

            if opinions_result.get('rows'):
                error_info['opinions'] = opinions_result['rows']

            # Get related sessions that dealt with this error type
            sessions_query = """
            SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entities e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attributes a ON eav.AttributeID = a.ID
                JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
            """
            sessions_result = await db_tool.execute_query(sessions_query, [f'%{error_type}%', f'%{error_type}%'])

            if sessions_result.get('rows'):
                error_info['related_sessions'] = sessions_result['rows']

            # Add recommendations based on facts
            if error_info['facts']:
                for fact in error_info['facts'][:3]:  # Top 3 facts
                    validity = float(fact.get('ValidityRating') or 0)
                    if validity > 70:
                        error_info['recommendations'].append({
                            'solution': fact.get('Name', 'Unknown solution'),
                            'effectiveness': validity,
                            'source': fact.get('DataSource', 'Unknown')
                        })

            # If no specific facts, provide general guidance
            if not error_info['recommendations']:
                error_info['recommendations'].append({
                    'solution': f'General troubleshooting for {self._get_meaningful_error_name(error_type)}',
                    'effectiveness': 50,
                    'source': 'Self-Healer Default Guidance'
                })

            return error_info

        except Exception as e:
            self.logger.error(f"Failed to get error type info for {error_type}: {e}")
            return {
                'error_type': error_type,
                'display_name': self._get_meaningful_error_name(error_type),
                'error': str(e),
                'description': f'Error retrieving information about {error_type}',
                'facts': [],
                'opinions': [],
                'solutions_count': 0,
                'average_effectiveness': 0,
                'recommendations': []
            }

    def _get_meaningful_error_name(self, error_type: str) -> str:
        """Convert technical error types to user-friendly names."""
        error_name_mapping = {
            'error_log_entry': 'General Error',
            'llm_communication': 'LLM Connection Error',
            'json_parsing': 'JSON Processing Error',
            'workflow_structure': 'Workflow Structure Error',
            'validation': 'Validation Error',
            'network': 'Network Error',
            'database': 'Database Error',
            'configuration': 'Configuration Error',
            'authentication': 'Authentication Error',
            'file_operations': 'File Operation Error',
            'performance': 'Performance Issue',
            'system': 'System Error'
        }

        # Try exact match first
        if error_type in error_name_mapping:
            return error_name_mapping[error_type]

        # Try partial matches
        for key, value in error_name_mapping.items():
            if key in error_type.lower():
                return value

        # Default: make it more readable
        return error_type.replace('_', ' ').title()

    def _extract_error_type_from_name(self, name: str) -> str:
        """Extract error type from fact/evidence names."""
        name_lower = name.lower()

        # Common error patterns in names
        if 'json' in name_lower:
            return 'JSON Processing Error'
        elif 'llm' in name_lower or 'connection' in name_lower:
            return 'LLM Connection Error'
        elif 'validation' in name_lower:
            return 'Validation Error'
        elif 'workflow' in name_lower:
            return 'Workflow Structure Error'
        elif 'database' in name_lower or 'db' in name_lower:
            return 'Database Error'
        elif 'network' in name_lower:
            return 'Network Error'
        elif 'config' in name_lower:
            return 'Configuration Error'
        else:
            return 'General Error'

    def _format_attribute_display(self, text_value: str, numeric_value: Optional[float], units: Optional[str]) -> str:
        """Format attribute values for display using numeric data when available."""
        if numeric_value is not None and units:
            # Use numeric value for better formatting
            if units == 'seconds':
                if numeric_value < 60:
                    return f"{numeric_value:.1f} seconds"
                elif numeric_value < 3600:
                    minutes = numeric_value / 60
                    return f"{minutes:.1f} minutes"
                else:
                    hours = numeric_value / 3600
                    return f"{hours:.1f} hours"
            elif units == 'percentage':
                return f"{numeric_value:.1f}%"
            elif units == 'milliseconds':
                return f"{numeric_value:.0f}ms"
            else:
                return f"{numeric_value} {units}"

        # Fallback to text value
        return text_value or 'Unknown'
    
    async def start(self):
        """Start the dashboard server."""
        if self.is_running:
            self.logger.warning("Dashboard is already running")
            return
        
        self.is_running = True
        
        # Start background update task
        self.update_task = asyncio.create_task(self._background_updates())
        
        # Start the web server
        config = uvicorn.Config(
            app=self.app,
            host="127.0.0.1",
            port=self.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        
        self.logger.info(f"Starting Self-Healer Dashboard on http://localhost:{self.port}")
        await server.serve()
    
    async def stop(self):
        """Stop the dashboard server."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Stop background task
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        
        # Close WebSocket connections
        for ws in self.websocket_connections:
            try:
                await ws.close()
            except:
                pass
        
        self.websocket_connections.clear()
        self.logger.info("Self-Healer Dashboard stopped")
    
    async def _background_updates(self):
        """Background task for periodic updates."""
        while self.is_running:
            try:
                # Update dashboard data
                self.dashboard_data = await self._get_dashboard_data()
                self.last_update = datetime.now()
                
                # Send updates to connected WebSocket clients
                if self.websocket_connections:
                    message = json.dumps(self.dashboard_data, cls=DateTimeEncoder)
                    disconnected = []

                    for ws in self.websocket_connections:
                        try:
                            await ws.send_text(message)
                        except ConnectionClosedError:
                            # Connection closed normally, don't log as error
                            disconnected.append(ws)
                        except Exception as e:
                            # Only log non-normal disconnections
                            error_str = str(e).lower()
                            if "1001" not in error_str and "1012" not in error_str and "service restart" not in error_str:
                                self.logger.debug(f"WebSocket send failed: {e}")
                            disconnected.append(ws)

                    # Remove disconnected clients
                    for ws in disconnected:
                        if ws in self.websocket_connections:
                            self.websocket_connections.remove(ws)
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in background updates: {e}")
                await asyncio.sleep(10)  # Wait longer on error


async def run_dashboard(healer_manager: SelfHealerManager, port: int = 8081):
    """Run the Self-Healer Dashboard."""
    dashboard = SelfHealerDashboard(healer_manager, port)
    await dashboard.start()
